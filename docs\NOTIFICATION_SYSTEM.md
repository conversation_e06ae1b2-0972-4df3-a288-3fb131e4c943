# Persistent Cross-Device Notification System

## Overview

This document explains the hybrid notification system that combines real-time WebSocket notifications with persistent database-backed notifications for cross-device synchronization.

## Database Schema Mapping

Your backend database uses these field names:
- `isRead` (boolean) - maps to frontend `read`
- `data` (JSON) - maps to frontend `metadata`
- `readAt` (timestamp) - when notification was read
- `priority` (enum) - 'low', 'medium', 'high'

## Architecture

### 1. Frontend-Only Notifications (Real-time)
- **Purpose**: Immediate display of real-time events
- **Storage**: Client-side Valtio store
- **Persistence**: None (lost on refresh)
- **Use cases**: Chat messages, live updates, temporary alerts

### 2. Backend Database Notifications (Persistent)
- **Purpose**: Cross-device synchronization and persistence
- **Storage**: Database with user association
- **Persistence**: Survives sessions and device switches
- **Use cases**: Important alerts, system notifications, booking confirmations

### 3. Hybrid System Benefits
- **Real-time responsiveness**: Immediate UI updates via WebSocket
- **Cross-device sync**: Notifications appear on all user devices
- **Optimistic updates**: Fast UI with background API sync
- **Offline resilience**: Works offline, syncs when reconnected

## API Endpoints Required

Your backend should implement:

```typescript
GET    /notifications              // Get user notifications
GET    /notifications/unread-count // Get unread count
PATCH  /notifications/:id/read     // Mark notification as read
PATCH  /notifications/mark-all-read // Mark all as read
DELETE /notifications/:id          // Delete notification
DELETE /notifications/clear-all    // Clear all notifications
POST   /notifications              // Create notification
```

## Database Schema Example

```sql
CREATE TABLE notifications (
  id VARCHAR PRIMARY KEY,
  userId VARCHAR NOT NULL,
  title VARCHAR NOT NULL,
  message TEXT NOT NULL,
  isRead BOOLEAN DEFAULT FALSE,
  readAt TIMESTAMP NULL,
  timestamp TIMESTAMP DEFAULT NOW(),
  link VARCHAR NULL,
  type VARCHAR NULL,
  priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
  data JSON NULL,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW() ON UPDATE NOW()
);
```

## Usage Examples

### Creating Persistent Notifications

```typescript
import { createNotification } from '@/api/notifications/data';

// Create a persistent notification
await createNotification({
  title: 'Package Booking Confirmed',
  message: 'Your booking #12345 has been confirmed',
  link: '/bookings/12345',
  type: 'booking_confirmation',
  priority: 'high',
  data: {
    bookingId: '12345',
    packageName: 'Health Checkup',
    scheduledDate: '2024-01-15'
  }
});
```

### Real-time WebSocket Notifications

```typescript
// Backend sends via WebSocket
socket.emit('new_notification', {
  title: 'New Message',
  message: 'You have a new message from Dr. Smith',
  link: '/messages/456',
  type: 'message',
  priority: 'medium',
  data: {
    senderId: 'dr-smith-123',
    messageId: '456'
  },
  persistent: true // Will also save to database
});
```

### Using the Notification Hook

```typescript
import { useNotifications } from '@/hooks/useNotifications';

function MyComponent() {
  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    refresh
  } = useNotifications();

  const handleNotificationClick = async (id: string) => {
    await markAsRead(id); // Optimistic update + API call
  };

  return (
    <div>
      <p>You have {unreadCount} unread notifications</p>
      {notifications.map(notification => (
        <div key={notification.id} onClick={() => handleNotificationClick(notification.id)}>
          <h4>{notification.title}</h4>
          <p>{notification.message}</p>
          {notification.metadata?.priority === 'high' && (
            <span className="high-priority-badge">High Priority</span>
          )}
        </div>
      ))}
    </div>
  );
}
```

## WebSocket Events

### Client Listening Events
- `new_notification` - New notification received
- `notification_sync` - Trigger sync with backend

### Server Should Emit
```typescript
// Real-time notification
socket.emit('new_notification', {
  title: string,
  message: string,
  link?: string,
  type?: string,
  priority?: 'low' | 'medium' | 'high',
  data?: object,
  persistent?: boolean // If true, also save to database
});

// Trigger sync across user's devices
socket.to(`user_${userId}`).emit('notification_sync');
```

## Cross-Device Synchronization Flow

1. **User A** receives notification on Device 1
2. **Backend** saves notification to database
3. **Backend** emits `notification_sync` to all user's connected devices
4. **Device 2** receives sync event and refreshes from backend
5. **Device 2** now shows the same notification with correct read/unread state

## Migration from Current System

1. **Keep existing WebSocket notifications** for real-time features
2. **Add backend API calls** for persistent notifications
3. **Update notification creation** to use backend for important notifications
4. **Implement sync logic** to handle cross-device state

The system is backward compatible - existing WebSocket notifications will continue to work as local notifications.
