import { proxy } from 'valtio';
import { BackendNotification } from '@/api/notifications/data';

export interface Notification {
  id: string;
  title: string;
  message: string;
  read: boolean;
  timestamp: Date;
  link?: string;
  type?: string;
  metadata?: Record<string, any>;
}

interface NotificationState {
  notifications: Notification[];
  isLoading: boolean;
  lastSyncTime: Date | null;

  // Local operations (for real-time updates)
  addNotification: (
    notification: Omit<Notification, 'id' | 'timestamp' | 'read'>
  ) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;

  // Backend sync operations
  syncWithBackend: (backendNotifications: BackendNotification[]) => void;
  setLoading: (loading: boolean) => void;
  updateLastSyncTime: () => void;
}

// Helper function to convert backend notification to frontend format
const convertBackendNotification = (backendNotification: BackendNotification): Notification => ({
  id: backendNotification.id,
  title: backendNotification.title,
  message: backendNotification.message,
  read: backendNotification.isRead,
  timestamp: new Date(backendNotification.timestamp),
  link: backendNotification.link,
  type: backendNotification.type,
  metadata: {
    ...backendNotification.data,
    priority: backendNotification.priority,
    readAt: backendNotification.readAt,
    createdAt: backendNotification.createdAt,
    updatedAt: backendNotification.updatedAt,
  },
});

export const notificationStore = proxy<NotificationState>({
  notifications: [],
  isLoading: false,
  lastSyncTime: null,

  // Local operations (for real-time updates)
  addNotification: (notification) => {
    const newNotification: Notification = {
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false,
      ...notification,
    };
    notificationStore.notifications.unshift(newNotification);
  },

  markAsRead: (id) => {
    const notification = notificationStore.notifications.find(
      (n) => n.id === id
    );
    if (notification) {
      notification.read = true;
    }
  },

  markAllAsRead: () => {
    notificationStore.notifications.forEach((notification) => {
      notification.read = true;
    });
  },

  removeNotification: (id) => {
    const index = notificationStore.notifications.findIndex((n) => n.id === id);
    if (index !== -1) {
      notificationStore.notifications.splice(index, 1);
    }
  },

  clearAll: () => {
    notificationStore.notifications = [];
  },

  // Backend sync operations
  syncWithBackend: (backendNotifications) => {
    const convertedNotifications = backendNotifications.map(convertBackendNotification);
    notificationStore.notifications = convertedNotifications;
    notificationStore.updateLastSyncTime();
  },

  setLoading: (loading) => {
    notificationStore.isLoading = loading;
  },

  updateLastSyncTime: () => {
    notificationStore.lastSyncTime = new Date();
  },
});
